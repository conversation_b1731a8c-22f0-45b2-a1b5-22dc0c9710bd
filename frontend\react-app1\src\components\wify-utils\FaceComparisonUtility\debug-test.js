/**
 * Debug Test for Face Comparison Utility
 * Run this in browser console to test the face comparison functionality
 */

// Test function to verify URL handling
const testUrlHandling = () => {
    console.log('=== Testing URL Handling ===');
    
    // Import the utility (this would need to be done differently in actual browser)
    // For now, assume FaceComparisonUtility is available globally
    
    const testCases = [
        'org_3/capture_image_271010116523600_e52fy5hPVRgnjpSQ.png',
        'http://localhost:3600/media/org_3/profile_picture.jpg',
        'https://example.com/image.jpg'
    ];
    
    testCases.forEach(testCase => {
        console.log(`Input: ${testCase}`);
        
        // Test getFullImageUrl
        const fullUrl = FaceComparisonUtility.getFullImageUrl(testCase);
        console.log(`Full URL: ${fullUrl}`);
        
        // Test addAuthorizationToUrl
        const authUrl = FaceComparisonUtility.addAuthorizationToUrl(fullUrl);
        console.log(`With Auth: ${authUrl}`);
        
        console.log('---');
    });
};

// Test function to verify camera files structure
const testCameraFilesStructure = () => {
    console.log('=== Testing Camera Files Structure ===');
    
    // Mock camera files as they would appear in TaskUpdateEditor
    const mockCameraFiles = {
        'selfie_field': [
            'org_3/capture_image_271010116523600_e52fy5hPVRgnjpSQ.png'
        ],
        'verification_photo': [
            'org_3/another_image_123456789.jpg'
        ]
    };
    
    console.log('Mock camera files:', mockCameraFiles);
    
    // Test field extraction
    const verificationFields = ['selfie_field', 'verification_photo'];
    
    verificationFields.forEach(fieldKey => {
        const fieldFiles = mockCameraFiles[fieldKey];
        console.log(`Field ${fieldKey}:`, fieldFiles);
        
        if (fieldFiles && fieldFiles.length > 0) {
            const firstFile = fieldFiles[0];
            console.log(`First file: ${firstFile} (type: ${typeof firstFile})`);
        }
    });
};

// Test function to verify profile picture URL handling
const testProfilePictureUrl = () => {
    console.log('=== Testing Profile Picture URL ===');
    
    // Mock localStorage user data
    const mockUser = {
        profile_picture_url: 'org_3/profile_picture_1762857864116_269320004952600_EylGBOI6QpCqUKH9.jpg'
    };
    
    console.log('Mock user data:', mockUser);
    
    // Simulate the profile picture URL conversion
    const profilePath = mockUser.profile_picture_url;
    console.log('Profile path:', profilePath);
    
    if (!profilePath.startsWith('http://') && !profilePath.startsWith('https://')) {
        const baseUrl = window.location.origin;
        const fullUrl = `${baseUrl}/media/${profilePath}`;
        console.log('Full profile URL:', fullUrl);
        
        // Test auth addition
        const authUrl = FaceComparisonUtility.addAuthorizationToUrl(fullUrl);
        console.log('Profile URL with auth:', authUrl);
    }
};

// Test function to verify auth token
const testAuthToken = () => {
    console.log('=== Testing Auth Token ===');
    
    try {
        // Check if http_utils is available
        if (typeof http_utils !== 'undefined') {
            const authToken = http_utils.getAuthToken();
            console.log('Auth token from http_utils:', authToken);
        } else {
            console.log('http_utils not available, checking localStorage...');
            
            const user = JSON.parse(localStorage.getItem('user'));
            console.log('User from localStorage:', user);
            
            if (user?.token) {
                console.log('Token from user object:', user.token);
            }
            
            const directToken = localStorage.getItem('token');
            console.log('Direct token from localStorage:', directToken);
        }
    } catch (error) {
        console.error('Error testing auth token:', error);
    }
};

// Main test function
const runDebugTests = () => {
    console.log('🔍 Running Face Comparison Debug Tests...');
    console.log('Current URL:', window.location.href);
    console.log('Origin:', window.location.origin);
    
    testAuthToken();
    testUrlHandling();
    testCameraFilesStructure();
    testProfilePictureUrl();
    
    console.log('✅ Debug tests completed. Check console output above.');
};

// Export for use in browser console
if (typeof window !== 'undefined') {
    window.FaceComparisonDebugTests = {
        runDebugTests,
        testAuthToken,
        testUrlHandling,
        testCameraFilesStructure,
        testProfilePictureUrl
    };
    
    console.log('🚀 Face Comparison Debug Tests loaded!');
    console.log('Run: window.FaceComparisonDebugTests.runDebugTests()');
}

export default {
    runDebugTests,
    testAuthToken,
    testUrlHandling,
    testCameraFilesStructure,
    testProfilePictureUrl
};
