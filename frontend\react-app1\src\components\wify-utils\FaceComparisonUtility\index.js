import React from 'react';
import { message } from 'antd';
import AWS from 'aws-sdk';

// AWS Configuration - same as FaceComparisonDemo
AWS.config.update({
    region: 'ap-south-1',
    accessKeyId: '********************',
    secretAccessKey: '9cnbd6RYgvGN73l2dfi/I1P5gqlYUmW3nJfbYLLA',
});

const rekognition = new AWS.Rekognition();

const REKOGNITION_CONFIG = {
    similarityThreshold: 70, // Minimum similarity percentage (0-100)
    maxFaces: 10, // Maximum number of faces to detect
    qualityFilter: 'AUTO', // AUTO, LOW, MEDIUM, HIGH
};

/**
 * Face Comparison Utility Class
 * Provides methods to compare faces using AWS Rekognition
 */
class FaceComparisonUtility {
    /**
     * Convert file to base64
     * @param {File|Blob} file - The file to convert
     * @returns {Promise<string>} Base64 string
     */
    static fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const base64 = reader.result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    /**
     * Convert URL to base64
     * @param {string} url - The image URL
     * @returns {Promise<string>} Base64 string
     */
    static urlToBase64(url) {
        return new Promise((resolve, reject) => {
            fetch(url)
                .then((response) => response.blob())
                .then((blob) => this.fileToBase64(blob))
                .then(resolve)
                .catch(reject);
        });
    }

    /**
     * Compare two faces using AWS Rekognition
     * @param {File|Blob|string} sourceImage - Source image (File/Blob or URL)
     * @param {File|Blob|string} targetImage - Target image (File/Blob or URL)
     * @param {number} similarityThreshold - Minimum similarity threshold (default: 70)
     * @returns {Promise<Object>} Comparison result
     */
    static async compareFaces(
        sourceImage,
        targetImage,
        similarityThreshold = REKOGNITION_CONFIG.similarityThreshold
    ) {
        try {
            // Convert images to base64
            let sourceBase64, targetBase64;

            if (typeof sourceImage === 'string') {
                // URL
                sourceBase64 = await this.urlToBase64(sourceImage);
            } else {
                // File/Blob
                sourceBase64 = await this.fileToBase64(sourceImage);
            }

            if (typeof targetImage === 'string') {
                // URL
                targetBase64 = await this.urlToBase64(targetImage);
            } else {
                // File/Blob
                targetBase64 = await this.fileToBase64(targetImage);
            }

            // Convert base64 to Buffer (AWS SDK v2 format)
            const sourceBuffer = Buffer.from(sourceBase64, 'base64');
            const targetBuffer = Buffer.from(targetBase64, 'base64');

            // Create the parameters for AWS SDK v2
            const params = {
                SourceImage: {
                    Bytes: sourceBuffer,
                },
                TargetImage: {
                    Bytes: targetBuffer,
                },
                SimilarityThreshold: similarityThreshold,
            };

            // Execute the API call using AWS SDK v2
            const response = await rekognition.compareFaces(params).promise();

            return {
                success: true,
                data: response,
                hasMatch:
                    response.FaceMatches && response.FaceMatches.length > 0,
                similarity:
                    response.FaceMatches && response.FaceMatches.length > 0
                        ? response.FaceMatches[0].Similarity
                        : 0,
                matchCount: response.FaceMatches
                    ? response.FaceMatches.length
                    : 0,
            };
        } catch (error) {
            console.error('Face comparison error:', error);
            return {
                success: false,
                error: error.message,
                hasMatch: false,
                similarity: 0,
                matchCount: 0,
            };
        }
    }

    /**
     * Validate face comparison result
     * @param {Object} result - Result from compareFaces
     * @param {number} minSimilarity - Minimum required similarity (default: 70)
     * @returns {Object} Validation result
     */
    static validateFaceComparison(
        result,
        minSimilarity = REKOGNITION_CONFIG.similarityThreshold
    ) {
        if (!result.success) {
            return {
                isValid: false,
                message: `Face comparison failed: ${result.error}`,
                similarity: 0,
            };
        }

        if (!result.hasMatch) {
            return {
                isValid: false,
                message: 'No matching faces found between the images',
                similarity: 0,
            };
        }

        if (result.similarity < minSimilarity) {
            return {
                isValid: false,
                message: `Face similarity (${result.similarity.toFixed(1)}%) is below required threshold (${minSimilarity}%)`,
                similarity: result.similarity,
            };
        }

        return {
            isValid: true,
            message: `Face verification successful (${result.similarity.toFixed(1)}% similarity)`,
            similarity: result.similarity,
        };
    }

    /**
     * Compare camera field images with profile picture
     * @param {Object} cameraFiles - Camera files object with sections
     * @param {Array} verificationFields - Array of field keys to verify
     * @param {string} profilePictureUrl - Profile picture URL
     * @param {number} minSimilarity - Minimum required similarity
     * @returns {Promise<Object>} Verification result
     */
    static async verifyCameraFieldsWithProfile(
        cameraFiles,
        verificationFields,
        profilePictureUrl,
        minSimilarity = REKOGNITION_CONFIG.similarityThreshold
    ) {
        console.log('verifyCameraFieldsWithProfile', cameraFiles);
        console.log('verifyCameraFieldsWithProfile', verificationFields);
        console.log('verifyCameraFieldsWithProfile', profilePictureUrl);
        console.log('verifyCameraFieldsWithProfile', minSimilarity);
        if (!profilePictureUrl) {
            return {
                success: false,
                message:
                    'Profile picture not found. Please upload a profile picture first.',
                results: [],
            };
        }

        if (!verificationFields || verificationFields.length === 0) {
            return {
                success: true,
                message: 'No verification fields specified',
                results: [],
            };
        }

        const results = [];
        let allValid = true;
        let errorMessages = [];

        for (const fieldKey of verificationFields) {
            const fieldFiles = cameraFiles[fieldKey];

            if (!fieldFiles || fieldFiles.length === 0) {
                allValid = false;
                errorMessages.push(`No image found for field: ${fieldKey}`);
                results.push({
                    fieldKey,
                    success: false,
                    message: `No image found for field: ${fieldKey}`,
                    similarity: 0,
                });
                continue;
            }

            // Use the first image from the field
            const firstFile = fieldFiles[0];
            let imageToCompare;

            if (firstFile.url) {
                // Use the URL if available
                imageToCompare = firstFile.url;
            } else if (firstFile.file) {
                // Use the file object
                imageToCompare = firstFile.file;
            } else {
                allValid = false;
                errorMessages.push(`Invalid image data for field: ${fieldKey}`);
                results.push({
                    fieldKey,
                    success: false,
                    message: `Invalid image data for field: ${fieldKey}`,
                    similarity: 0,
                });
                continue;
            }

            try {
                const comparisonResult = await this.compareFaces(
                    imageToCompare,
                    profilePictureUrl,
                    minSimilarity
                );
                const validation = this.validateFaceComparison(
                    comparisonResult,
                    minSimilarity
                );

                results.push({
                    fieldKey,
                    success: validation.isValid,
                    message: validation.message,
                    similarity: validation.similarity,
                    comparisonData: comparisonResult.data,
                });

                if (!validation.isValid) {
                    allValid = false;
                    errorMessages.push(`${fieldKey}: ${validation.message}`);
                }
            } catch (error) {
                allValid = false;
                const errorMsg = `Face comparison failed for ${fieldKey}: ${error.message}`;
                errorMessages.push(errorMsg);
                results.push({
                    fieldKey,
                    success: false,
                    message: errorMsg,
                    similarity: 0,
                });
            }
        }

        return {
            success: allValid,
            message: allValid
                ? 'All face verifications passed successfully'
                : `Face verification failed: ${errorMessages.join('; ')}`,
            results,
            errorMessages,
        };
    }
}

export default FaceComparisonUtility;
